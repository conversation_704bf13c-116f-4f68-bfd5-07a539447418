/*!
 * @file
 * @brief Main program.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stdint.h>
#include "Init_Mcu.h"
#include "adpt_iwdg.h"
#include "Timer.h"
#include "TinyTimer.h"
#include "Adpt_Timebase.h"

#include "hc32l186.h" // HC32L186 HAL header
#include "ASSERT.h"

#include "SEGGER_RTT.h"

#define log_printf SEGGER_RTT_printf
#define log_WriteString SEGGER_RTT_WriteString

// Timer module and timer instances
// static TimerModule_st st_TimerModule;
// static Timer_st st_Timer1;
// static Timer_st st_Timer2;

// TinyTimer module and timer instances
static TinyTimerModule_t st_TinyTimerModule;
static TinyTimer_t st_TinyTimer1;
static TinyTimer_t st_TinyTimer2;

static uint32_t u32_count;

static void MyTinyTimerCallback(void *context, TinyTimerModule_t *timerModule)
{
     log_printf(0, "MyTinyTimerCallback 1 in isr count:%d\n", u32_count++);
}

static void MyTinyTimerCallback2(void *context, TinyTimerModule_t *timerModule)
{
     log_printf(0, "MyTinyTimerCallback 2 in isr count:%d\n", u32_count++);
}


int main(void)
{
    Init_Mcu();

    //Initialize SysTick time source with 1ms ticks
    SysTickTimeSource_Init();

    // Initialize timer module with SysTick time source
/*     TimerModule_Init(&st_TimerModule, (I_TimeSource_st *)GetTimeSource());

    TimerModule_StartPeriodic(&st_TimerModule, &st_Timer1, 1000, MyTimerCallback, NULL);
    TimerModule_StartPeriodic(&st_TimerModule, &st_Timer2, 1000, MyTimerCallback2, NULL);

    while(1)
    {
        IWDG_Refesh();
        // Run timer module
        TimerModule_Run(&st_TimerModule);
    } */

      // Initialize tiny timer module with SysTick time source
    TinyTimerModule_Init(&st_TinyTimerModule, GetTinyTimeSource());

    TinyTimerModule_StartPeriodic(&st_TinyTimerModule, &st_TinyTimer1, 1000, MyTinyTimerCallback, NULL);
    TinyTimerModule_StartPeriodic(&st_TinyTimerModule, &st_TinyTimer2, 1000, MyTinyTimerCallback2, NULL);

    while(1)
    {
        IWDG_Refesh();
        // Run tiny timer module
        TinyTimerModule_Run(&st_TinyTimerModule);
    }
  
    return (1);
}
