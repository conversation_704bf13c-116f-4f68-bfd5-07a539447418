/*!
 * @file
 * @brief Statically allocated singly linked list. Nodes are allocated by
 * clients.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#ifndef LINKEDLIST_H
#define LINKEDLIST_H

#include <stdbool.h>
#include <stddef.h>

/*!
 * @note This will generally be used within another struct to make that struct a
 * linked list node.
 */
typedef struct LinkedListNode_st
{
    struct
    {
        struct LinkedListNode_st *next;
    } _private;
} LinkedListNode_st;

typedef struct
{
    struct
    {
        LinkedListNode_st *head;
    } _private;
} LinkedList_st;

typedef struct
{
    struct
    {
        LinkedListNode_st *current;
    } _private;
} LinkedListIterator_st;

/*!
 * Initialize a linked list to an empty list.
 * @param list
 */
void LinkedList_Init(LinkedList_st *list);

/*!
 * Count the nodes in a list.
 * @param list
 * @returns The number of nodes in the list.
 */
size_t LinkedList_Count(const LinkedList_st *list);

/*!
 * Get the node at a specific location in a list.
 * @pre index < Count(list)
 * @param list
 * @param index
 * @returns The node at the specified location..
 */
LinkedListNode_st *LinkedList_At(const LinkedList_st *list, const size_t index);

/*!
 * Get the index of a node within a list.
 * @pre Contains(list, node)
 * @param list
 * @param node
 * @returns The index of the node within the list.
 */
size_t LinkedList_IndexOf(const LinkedList_st *list,
    const LinkedListNode_st *node);

/*!
 * Determine whether a node is contained in a list.
 * @param list
 * @param node
 * @returns True if the node is contained in the list, false otherwise.
 */
bool LinkedList_Contains(const LinkedList_st *list,
    const LinkedListNode_st *node);

/*!
 * Add a node to the front (head) of a list.
 * @pre !Contains(list, node)
 * @param list
 * @param node
 */
void LinkedList_PushFront(LinkedList_st *list, LinkedListNode_st *node);

/*!
 * Add a node to the back (tail) of a list.
 * @pre !Contains(list, node)
 * @param list
 * @param node
 */
void LinkedList_PushBack(LinkedList_st *list, LinkedListNode_st *node);

/*!
 * Add a node at the specified index in the list.
 * @pre !Contains(list, node)
 * @pre index <= Count(list)
 * @param list
 * @param node
 */
void LinkedList_Insert(LinkedList_st *list, LinkedListNode_st *node, size_t index);

/*!
 * Remove a node from the front (head) of a list.
 * @pre Count(list) > 0
 * @param list
 * @returns The removed node.
 */
LinkedListNode_st *LinkedList_PopFront(LinkedList_st *list);

/*!
 * Remove a node from the back (tail) of a list.
 * @pre Count(list) > 0
 * @param list
 * @returns The removed node.
 */
LinkedListNode_st *LinkedList_PopBack(LinkedList_st *list);

/*!
 * Remove a specific node from a list.
 * @pre Initialized(list)
 * @param list
 * @param node
 */
void LinkedList_Remove(LinkedList_st *list, LinkedListNode_st *node);

/*!
 * Remove the node at a specific index from a list.
 * @pre index < Count(list)
 * @param list
 * @param index The index.
 */
void LinkedList_RemoveAt(LinkedList_st *list, const size_t index);

/*!
 * Initialize an iterator for the provided list.
 * @param instance
 * @param list
 */
void LinkedListIterator_Init(LinkedListIterator_st *instance,
    LinkedList_st *list);

/*!
 * Return a pointer to the next node or NULL if there are no more nodes.
 * @param instance
 * @return
 */
LinkedListNode_st *LinkedListIterator_Next(LinkedListIterator_st *instance);

#define LinkedList_ForEach(_list, _type, _item, ...)            \
    do                                                          \
    {                                                           \
        LinkedListIterator_st _it;                               \
        LinkedListIterator_Init(&_it, _list);                   \
        _type *_item;                                           \
        while((_item = (_type *)LinkedListIterator_Next(&_it))) \
        {                                                       \
            __VA_ARGS__                                         \
        }                                                       \
    } while(0)

#endif
