/*!
 * @file
 * @brief Simple hierarchical state machine.
 *
 * This module does not implement all of UML state charts -- this was a
 * conscious design decision to drive simplicity.  In particular it eschews the
 * Init signal and init pseudo-state in favor of having a entry to a defined
 * initial state at HSM init.  This allows the same pattern to be implemented,
 * but introduces fewer concepts.
 *
 * Quick Overview:
 *
 *    States
 *       States are modeled by functions.  The function for a state is invoked
 * when a signal/event is sent to the state.  The state can determine whether
 * this signal is propagated by appropriately setting its return value.
 *
 *    Signals
 *       Signals are sent to states to indicate what event occurred.  If any
 * data is required, it will be accessible via void *data, but it is up to the
 * receiving state to interpret the void * appropriately based on the received
 * signal.
 *
 *       Signals are propagated to parent states if not consumed by the active
 * state.  Signals may be propagated in this way until all parents are
 * exhausted.  Alternately, a state can choose to consume a signal so that it is
 * not propagated.  In general a state will consume a signal that it acts upon
 * and defer signals that it does not act upon.  The exceptions to these rules
 * are the entry and exit signals -- they are always assumed to be consumed and
 * will not be propagated.
 *
 *    Transitions
 *       Self-Transitions
 *          If the current state is the target state then the exit signal is
 * sent to the current state and then the entry is sent to the current state.
 *
 *       Non-Self-Transitions
 *          Exit signals are sent to the current state and its parents until the
 * nearest common parent of the current and target state.  Then entry signals
 * are sent to all states on a direct path to the target state.  Finally an
 * entry signal is sent to the target state.
 *
 *       Examples:
 *
 *       = State A ====================  = State D ====================
 *       =                            =  =                            =
 *       =  = State B =  = State C =  =  =  = State E =  = State F =  =
 *       =  =         =  =         =  =  =  =         =  =         =  =
 *       =  =         =  =         =  =  =  =         =  =         =  =
 *       =  ===========  ===========  =  =  ===========  ===========  =
 *       =                            =  =                            =
 *       ==============================  ==============================
 *
 *          State Descriptor Table for the above state machine:
 *             {
 *                { StateA, HSM_NO_PARENT },
 *                   { StateB, StateA },
 *                   { StateC, StateA },
 *                { StateD, HSM_NO_PARENT },
 *                   { StateE, StateD },
 *                   { StateF, StateD },
 *             }
 *
 *          Transition from State B to State E
 *             Exit -> State B
 *             Exit -> State A
 *             Entry -> State D
 *             Entry -> State E
 *
 *          Transition from State B to State C
 *             Exit -> State B
 *             Entry -> State C
 *
 *          Transition from State A to State F
 *             Exit -> State A
 *             Entry -> State D
 *             Entry -> State F
 *
 * @warning A transition cannot occur on entry to a state.  This guarantees that
 * any signals will be delivered to the correct state without any ambiguities.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#ifndef HSM_H
#define HSM_H

#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>

#define HSM_NO_PARENT (NULL)
#define HSM_SIGNAL_CONSUMED (true)
#define HSM_SIGNAL_DEFERRED (false)

#define HsmNoParent HSM_NO_PARENT

/*!
 * Common signals.
 */
enum
{
    HSM_ENTRY, //!< HSM_ENTRY Signal sent upon entry to a state.  Signal cannot
    //!< propagate.
    HSM_EXIT, //!< HSM_EXIT Signal sent upon exit from a state.  Signal cannot
    //!< propagate.
    HSM_USER_SIGNAL_START, //!< HSM_USER_SIGNAL_START Start of user-defined
    //!< signals.  All user signals must have an integer
    //!< value >= this.

    Hsm_Entry = HSM_ENTRY,
    Hsm_Exit = HSM_EXIT,
    Hsm_UserSignalStart = HSM_USER_SIGNAL_START,

    HsmSignalConsumed = HSM_SIGNAL_CONSUMED,
    HsmSignalDeferred = HSM_SIGNAL_DEFERRED
};

/*!
 * Signal sent to an HSM state.
 */
typedef uint16_t HsmSignal_t;

/*!
 * Index of a state within the state descriptor list.
 */
typedef uint16_t HsmStateIndex_t;

struct Hsm_st;

/*!
 * HSM state represented as a function.  The state is invoked to handle signals
 * (events).
 * @param hsm The instance of the HSM handling the signal.
 * @param signal The received signal.
 * @param data Signal data.
 * @return The state should return HSM_SIGNAL_CONSUMED if it 'uses' the received
 * signal and does the signal should not be propagated to the parent state.  The
 * state shoudl return HSM_SIGNAL_DEFERRED in order to allow the signal to
 * propagate to the parent state.  This return value is ignored for ENTRY/EXIT
 * signals because they are not propagated.
 */
typedef bool (*pfnHsmState_t)(struct Hsm_st *hsm, const HsmSignal_t signal, const void *data);

/*!
 * HSM state descriptor.  Declares a state's existence as well as its parent.
 */
typedef struct
{
    /*!
   * The state.
   */
    pfnHsmState_t state;

    /*!
   * The parent state.  If the state has no parent, use HSM_NO_PARENT.
   */
    pfnHsmState_t parent;
} HsmStateHierarchyDescriptor_st;

typedef struct
{
    const HsmStateHierarchyDescriptor_st *stateHierarchyDescriptorList;
    uint16_t stateHierarchyDescriptorListSize;
} HsmConfiguration_st;

/*!
 * Hierarchical state machine.  Struct members should be considered private.
 */
typedef struct Hsm_st
{
    struct
    {
        const HsmConfiguration_st *configuration;
        HsmStateIndex_t currentStateIndex;
        bool transitionActive;
    } _private;
} Hsm_st;

/*!
 * Initialize an HSM.  This will cause entry to the initial state.  This will be
 * most likely be called at application startup and should not be confused with
 * the init signal in a UML state chart.
 * @pre The initial state is contained in the stateDescriptorList.
 * @pre Parent states declared in stateDescriptorList are valid.
 * @param instance The HSM.
 * @param configuration List of states and state parents.
 * @param initialState Initial state.
 */
void Hsm_Init(Hsm_st *instance, const HsmConfiguration_st *configuration, pfnHsmState_t initialState);

/*!
 * Send a signal to the current state.  If the current state does not consume
 * the signal it will be sent to each parent until it is consumed or the parents
 * are exhausted.
 * @pre instance != NULL
 * @pre signal != HSM_ENTRY
 * @pre signal != HSM_EXIT
 * @param instance The HSM.
 * @param signal The signal to send.
 * @param data The signal data to send.
 */
void Hsm_SendSignal(Hsm_st *instance, const HsmSignal_t signal, const void *data);

/*!
 * Transition to a state in the HSM.  Entry and exit signals are sent to all
 * states 'connecting' the current and target states except the common parent.
 * However, if the current state is the target (ie: self-transition) then the
 * current state will receive an exit and entry signal (even though it is its
 * own common parent).
 * @pre instance != NULL
 * @pre targetState != NULL
 * @pre The target state is contained in the stateDescriptorList.
 * @pre A transition is not active.
 * @param instance The HSM.
 * @param targetState The state to transition to.
 */
void Hsm_Transition(Hsm_st *instance, pfnHsmState_t targetState);

#endif
