/*!
 * @file
 * @brief system timer.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#include "SystemTimerModule.h"
#include "Core_Types.h"

// Number of milliseconds in 1 second (Defined just for code readability)
#define U16_NUM_MILLISECONDS_PER_SECOND ((uint16_t)(1000))
// Number of seconds in 1 minute (Defined just for code readability)
#define U16_NUM_SECONDS_PER_MINUTE ((uint16_t)(60))
// Number of seconds in 1 minute (Defined just for code readability)
#define U8_NUM_SECONDS_PER_MINUTE ((uint8_t)(60))
// Number of minutes in 1 hour (Defined just for code readability)
#define U16_NUM_MINUTES_PER_HOUR ((uint16_t)(60))
// Number of minutes in 1 hour (Defined just for code readability)
#define U8_NUM_MINUTES_PER_HOUR ((uint8_t)(60))
// Number of hours in 1 day (Defined just for code readability)
#define U16_NUM_HOURS_PER_DAY ((uint16_t)(24))
// Number of hours in 1 day (Defined just for code readability)
#define U8_NUM_HOURS_PER_DAY ((uint8_t)(24))
// Number of days in 1 month (Defined just for code readability)
#define U8_NUM_DAYS_PER_MONTH ((uint8_t)(30))

static const I_SystemTimer_Api_st Api = {
    Get_DayCount,
    Get_HourCount,
    Get_MinuteCount,
    Get_SecondCount,
    Get_MSecCount,
    Get_DayElapsedTime,
    Get_HourElapsedTime,
    Get_MinuteElapsedTime,
    Get_SecondElapsedTime,
    Get_MSecElapsedTime,
    Shorten_Timer,
    Add_MSecCount
};

SystemTimer_st st_SystemTimer;

void Init_SystemTimer(void)
{
    st_SystemTimer.Interface.Api = &Api;

    st_SystemTimer._private.u16_DayCount = 0;
    st_SystemTimer._private.u16_HourCount = 0;
    st_SystemTimer._private.u16_MinuteCount = 0;
    st_SystemTimer._private.u16_SecondCount = 0;
    st_SystemTimer._private.u16_MSecCount = 0;

    st_SystemTimer._private.st_Clock.u8_Day = 0;
    st_SystemTimer._private.st_Clock.u8_Hour = 0;
    st_SystemTimer._private.st_Clock.u8_Minute = 0;
    st_SystemTimer._private.st_Clock.u8_Second = 0;
    st_SystemTimer._private.st_Clock.u16_MSec = 0;
}

uint16_t Get_DayCount(void)
{
    return (st_SystemTimer._private.u16_DayCount);
}

uint16_t Get_HourCount(void)
{
    return (st_SystemTimer._private.u16_HourCount);
}

uint16_t Get_MinuteCount(void)
{
    return (st_SystemTimer._private.u16_MinuteCount);
}

uint16_t Get_SecondCount(void)
{
    return (st_SystemTimer._private.u16_SecondCount);
}

uint16_t Get_MSecCount(void)
{
    return (st_SystemTimer._private.u16_MSecCount);
}

uint16_t Get_DayElapsedTime(uint16_t u16_dayStartTime)
{
    uint16_t u16_dayElapsedTime = 0;

    u16_dayElapsedTime = (uint16_t)(st_SystemTimer._private.u16_DayCount - u16_dayStartTime);

    return (u16_dayElapsedTime);
}

uint16_t Get_HourElapsedTime(uint16_t u16_hourStartTime)
{
    uint16_t u16_hourElapsedTime = 0;

    u16_hourElapsedTime = (uint16_t)(st_SystemTimer._private.u16_HourCount - u16_hourStartTime);

    return (u16_hourElapsedTime);
}

uint16_t Get_MinuteElapsedTime(uint16_t u16_minuteStartTime)
{
    uint16_t u16_minuteElapsedTime = 0;

    u16_minuteElapsedTime = (uint16_t)(st_SystemTimer._private.u16_MinuteCount - u16_minuteStartTime);

    return (u16_minuteElapsedTime);
}

uint16_t Get_SecondElapsedTime(uint16_t u16_secondStartTime)
{
    uint16_t u16_secondElapsedTime = 0;

    u16_secondElapsedTime = (uint16_t)(st_SystemTimer._private.u16_SecondCount - u16_secondStartTime);

    return (u16_secondElapsedTime);
}

uint16_t Get_MSecElapsedTime(uint16_t u16_msecStartTime)
{
    uint16_t u16_msecElapsedTime = 0;

    u16_msecElapsedTime = (uint16_t)(st_SystemTimer._private.u16_MSecCount - u16_msecStartTime);

    return (u16_msecElapsedTime);
}

void Shorten_Timer(const uint16_t u16_minute)
{
    uint32_t u32_time = 0;
    uint16_t u16_time = 0;

    st_SystemTimer._private.st_Clock.u16_MSec--;

    // Shorten Minute
    st_SystemTimer._private.u16_MinuteCount += u16_minute;

    // Shorten Second
    u32_time = ((uint32_t)u16_minute) * ((uint32_t)U16_NUM_SECONDS_PER_MINUTE) +
        ((uint32_t)st_SystemTimer._private.u16_SecondCount);
    if(u32_time > ((uint32_t)MAX_UINT16))
    {
        st_SystemTimer._private.u16_SecondCount = MAX_UINT16 -
            st_SystemTimer._private.u16_SecondCount;
    }
    else
    {
        st_SystemTimer._private.u16_SecondCount = (uint16_t)u32_time;
    }

    // Shorten Hour
    u32_time = ((uint32_t)u16_minute) +
        ((uint16_t)st_SystemTimer._private.st_Clock.u8_Minute);
    st_SystemTimer._private.st_Clock.u8_Minute =
        (uint8_t)(u32_time % ((uint32_t)U16_NUM_MINUTES_PER_HOUR));
    u16_time = (uint16_t)(u32_time / ((uint32_t)U16_NUM_MINUTES_PER_HOUR));
    st_SystemTimer._private.u16_HourCount += u16_time;

    u16_time += (uint16_t)st_SystemTimer._private.st_Clock.u8_Hour;
    st_SystemTimer._private.st_Clock.u8_Hour =
        (uint8_t)(u16_time % U16_NUM_HOURS_PER_DAY);

    // Shorten Day
    u16_time = u16_time / U16_NUM_HOURS_PER_DAY;
    st_SystemTimer._private.u16_DayCount += u16_time;

    st_SystemTimer._private.st_Clock.u8_Day += (uint8_t)u16_time;
    st_SystemTimer._private.st_Clock.u8_Day %= U8_NUM_DAYS_PER_MONTH;
}

void Add_MSecCount(void)
{
    st_SystemTimer._private.u16_MSecCount++;

    st_SystemTimer._private.st_Clock.u16_MSec++;
    if(st_SystemTimer._private.st_Clock.u16_MSec >= U16_NUM_MILLISECONDS_PER_SECOND)
    {
        // One Second
        st_SystemTimer._private.st_Clock.u16_MSec = 0;
        st_SystemTimer._private.u16_SecondCount++;
        st_SystemTimer._private.st_Clock.u8_Second++;
        if(st_SystemTimer._private.st_Clock.u8_Second >= U8_NUM_SECONDS_PER_MINUTE)
        {
            // One Minute
            st_SystemTimer._private.st_Clock.u8_Second = 0;
            st_SystemTimer._private.u16_MinuteCount++;
            st_SystemTimer._private.st_Clock.u8_Minute++;
            if(st_SystemTimer._private.st_Clock.u8_Minute >= U8_NUM_MINUTES_PER_HOUR)
            {
                // One Hour
                st_SystemTimer._private.st_Clock.u8_Minute = 0;
                st_SystemTimer._private.u16_HourCount++;
                st_SystemTimer._private.st_Clock.u8_Hour++;
                if(st_SystemTimer._private.st_Clock.u8_Hour >= U8_NUM_HOURS_PER_DAY)
                {
                    // One Day
                    st_SystemTimer._private.st_Clock.u8_Hour = 0;
                    st_SystemTimer._private.u16_DayCount++;
                    st_SystemTimer._private.st_Clock.u8_Day++;
                    if(st_SystemTimer._private.st_Clock.u8_Day >= U8_NUM_DAYS_PER_MONTH)
                    {
                        st_SystemTimer._private.st_Clock.u8_Day = 0;
                    }
                }
            }
        }
    }
}
