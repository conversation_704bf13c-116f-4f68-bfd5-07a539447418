/*!
 * @file
 * @brief This is the header file for the system timer module.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#ifndef __SYSTEMTIMERMODULE_H__
#define __SYSTEMTIMERMODULE_H__

#include <stdbool.h>
#include <stdint.h>

struct I_SystemTimer_Api_st;

typedef struct I_SystemTimer_st
{
    const struct I_SystemTimer_Api_st *Api;
} I_SystemTimer_st;

typedef struct Clock_st
{
    uint8_t u8_Day;
    uint8_t u8_Hour;
    uint8_t u8_Minute;
    uint8_t u8_Second;
    uint16_t u16_MSec;
} Clock_st;

typedef struct SystemTimer_st
{
    I_SystemTimer_st Interface;
    struct
    {
        Clock_st st_Clock;
        uint16_t u16_DayCount;
        uint16_t u16_HourCount;
        uint16_t u16_MinuteCount;
        uint16_t u16_SecondCount;
        uint16_t u16_MSecCount;
    } _private;
} SystemTimer_st;

typedef struct I_SystemTimer_Api_st
{
    uint16_t (*p_Get_DayCount)(void);
    uint16_t (*p_Get_HourCount)(void);
    uint16_t (*p_Get_MinuteCount)(void);
    uint16_t (*p_Get_SecondCount)(void);
    uint16_t (*p_Get_MSecCount)(void);
    uint16_t (*p_Get_DayElapsedTime)(uint16_t u16_dayStartTime);
    uint16_t (*p_Get_HourElapsedTime)(uint16_t u16_hourStartTime);
    uint16_t (*p_Get_MinuteElapsedTime)(uint16_t u16_minuteStartTime);
    uint16_t (*p_Get_SecondElapsedTime)(uint16_t u16_secondStartTime);
    uint16_t (*p_Get_MSecElapsedTime)(uint16_t u16_msecStartTime);

    void (*p_Shorten_Timer)(const uint16_t u16_minute);

    void (*p_Add_MSecCount)(void);
} I_SystemTimer_Api_st;

/**
 * @brief Initialize the system timer.
 *
 * @details This function sets up the system timer by populating the Api
 *          field of the Interface structure with the addresses of the
 *          functions that implement the system timer's API.  It also
 *          initializes the private fields of the system timer to zero.
 */
void Init_SystemTimer(void);

/**
 * @brief Get the current day count.
 *
 * @details This function returns the current day count in the system timer.
 *
 * @returns The current day count.
 */
uint16_t Get_DayCount(void);

/**
 * @brief Get the current hour count.
 *
 * @details This function returns the current hour count in the system timer.
 *
 * @returns The current hour count.
 */
uint16_t Get_HourCount(void);

/**
 * @brief Get the current minute count.
 *
 * @details This function returns the current minute count in the system timer.
 *
 * @returns The current minute count.
 */
uint16_t Get_MinuteCount(void);

/**
 * @brief Get the current second count.
 *
 * @details This function returns the current second count in the system timer.
 *
 * @returns The current second count.
 */
uint16_t Get_SecondCount(void);

/**
 * @brief Get the current millisecond count.
 *
 * @details This function returns the current millisecond count in the system timer.
 *
 * @returns The current millisecond count.
 */
uint16_t Get_MSecCount(void);

/**
 * @brief Calculate the elapsed days since a given start time.
 *
 * @param u16_dayStartTime The starting day count from which the elapsed time is calculated.
 * @return The number of days elapsed since the start time.
 */
uint16_t Get_DayElapsedTime(uint16_t u16_dayStartTime);

/**
 * @brief Calculate the elapsed hours since a given start time.
 *
 * @param u16_hourStartTime The starting hour count from which the elapsed time is calculated.
 * @return The number of hours elapsed since the start time.
 */
uint16_t Get_HourElapsedTime(uint16_t u16_hourStartTime);

/**
 * @brief Calculate the elapsed minutes since a given start time.
 * @param u16_minuteStartTime The starting minute count from which the elapsed time is calculated.
 * @return The number of minutes elapsed since the start time.
 */
uint16_t Get_MinuteElapsedTime(uint16_t u16_minuteStartTime);

/**
 * @brief Calculate the elapsed seconds since a given start time.
 * @param u16_secondStartTime The starting second count from which the elapsed time is calculated.
 * @return The number of seconds elapsed since the start time.
 */
uint16_t Get_SecondElapsedTime(uint16_t u16_secondStartTime);

/**
 * @brief Calculate the elapsed milliseconds since a given start time.
 * @param u16_msecStartTime The starting millisecond count from which the elapsed time is calculated.
 * @return The number of milliseconds elapsed since the start time.
 */
uint16_t Get_MSecElapsedTime(uint16_t u16_msecStartTime);

/**
 * @brief Shortens the system timer by a specified number of minutes.
 *
 * @details Decreases the millisecond count and adjusts the minute, second,
 *          hour, and day counts accordingly. The function ensures that the
 *          limits. This operation effectively reduces the timer by the given
 *          number of minutes, updating the internal clock structure.
 *
 * @param u16_minute The number of minutes to shorten the timer by.
 */
void Shorten_Timer(const uint16_t u16_minute);

/**
 * @brief Add one millisecond to the system timer.
 *
 * @details Increment the millisecond count and if one second has been reached, increment the second count.
 *          If one minute has been reached, increment the minute count, and so on.
 */
void Add_MSecCount(void);

/******************************************************************************
******************************************************************************/

#endif /* __SYSTEMTIMERMODULE_H__ */
