/*!
 * @file
 * @brief Implementation of an HsmSignalDelayer.
 *
 * This component uses a OneShotTimer with initial value of 0 to delay publishing
 * a signal.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#include "HsmSignalDelayer.h"
#include "uassert.h"
#include "utils.h"

static void SendDelayedSignal(void *context)
{
    REINTERPRET(instance, context, HsmSignalDelayer_st *);
    Hsm_SendSignal(instance->_private.delayedSignalContext.hsm, instance->_private.delayedSignalContext.signal, NULL);
}

static bool DelayedSignalPending(HsmSignalDelayer_st *instance)
{
    return TimerModule_IsRunning(instance->_private.delayConfig.timerModule, &instance->_private.delayConfig.delayTimer);
}

void HsmSignalDelayer_SendDelayedSignal(HsmSignalDelayer_st *instance, const HsmSignal_t signal)
{
    uassert(!DelayedSignalPending(instance));

    instance->_private.delayedSignalContext.signal = signal;

    TimerModule_StartOneShot(
        instance->_private.delayConfig.timerModule,
        &instance->_private.delayConfig.delayTimer,
        0,
        SendDelayedSignal,
        instance);
}

void HsmSignalDelayer_Init(HsmSignalDelayer_st *instance, Hsm_st *hsm, TimerModule_st *timerModule)
{
    instance->_private.delayedSignalContext.hsm = hsm;
    instance->_private.delayConfig.timerModule = timerModule;
}
