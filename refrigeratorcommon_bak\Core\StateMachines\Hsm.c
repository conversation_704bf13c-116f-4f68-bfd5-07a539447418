/*!
 * @file
 * @brief Implementation of a simple hierarchical state machine.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#include "Hsm.h"
#include "uassert.h"

#define INDEX_TOP ((HsmStateIndex_t)(((HsmStateIndex_t)0) - 1))

static HsmStateIndex_t GetStateIndex(Hsm_st *instance, pfnHsmState_t state)
{
    HsmStateIndex_t index;

    for(index = 0; index < instance->_private.configuration->stateHierarchyDescriptorListSize; index++)
    {
        if(state == instance->_private.configuration->stateHierarchyDescriptorList[index].state)
        {
            return index;
        }
    }

    return INDEX_TOP;
}

static pfnHsmState_t GetStateFromStateIndex(Hsm_st *instance, HsmStateIndex_t index)
{
    return instance->_private.configuration->stateHierarchyDescriptorList[index].state;
}

static HsmStateIndex_t GetParentStateIndex(Hsm_st *instance, const HsmStateIndex_t childIndex)
{
    return GetStateIndex(instance, instance->_private.configuration->stateHierarchyDescriptorList[childIndex].parent);
}

static void VerifyThatParentStatesAreValid(Hsm_st *instance)
{
    HsmStateIndex_t index;

    for(index = 0; index < instance->_private.configuration->stateHierarchyDescriptorListSize; index++)
    {
        if(instance->_private.configuration->stateHierarchyDescriptorList[index].parent)
        {
            uassert(INDEX_TOP != GetStateIndex(instance, instance->_private.configuration->stateHierarchyDescriptorList[index].parent));
        }
    }
}

void Hsm_SendSignal(Hsm_st *instance, const HsmSignal_t signal, const void *data)
{
    uassert(signal >= HSM_USER_SIGNAL_START);

    {
        HsmStateIndex_t currentIndex = instance->_private.currentStateIndex;
        bool result;

        // If signal is deferred send it to parent until it is consumed or we run out of parents
        do
        {
            result = GetStateFromStateIndex(instance, currentIndex)(instance, signal, data);
            currentIndex = GetParentStateIndex(instance, currentIndex);
        } while((result == HSM_SIGNAL_DEFERRED) && (currentIndex != INDEX_TOP));
    }
}

static HsmStateIndex_t GetNearestCommonParent(Hsm_st *instance, const HsmStateIndex_t index1, const HsmStateIndex_t index2)
{
    HsmStateIndex_t searchIndex1;
    HsmStateIndex_t searchIndex2 = index2;

    // Search for the nearest common parent of index1 and index2 by traversing the parent hierarchy
    // Traverse the full hierarchy for index1 for each successive parent of index2 until we hit the
    // top (no common parent) or we find a matching index (common parent identified)
    do
    {
        searchIndex1 = index1;

        // Cruise through the parents of index1 to see if we can find a match for the current index2 parent
        while((searchIndex1 != searchIndex2) && (searchIndex1 != INDEX_TOP))
        {
            // Match not found, go up one parent
            searchIndex1 = GetParentStateIndex(instance, searchIndex1);
        }

        // Did we not find a match?
        if(searchIndex1 != searchIndex2)
        {
            // No match found, go up one parent
            searchIndex2 = GetParentStateIndex(instance, searchIndex2);
        }
    } while(searchIndex1 != searchIndex2);

    return searchIndex2;
}

// From must be a child of until
// Exit and send exit signals to all states in between from and until in the state hierarchy
// From will be the first state to receive exit, until will be the first state to not receive an exit
static void PerformExits(Hsm_st *instance, const HsmStateIndex_t from, const HsmStateIndex_t until)
{
    HsmStateIndex_t current = from;

    while(current != until)
    {
        GetStateFromStateIndex(instance, current)(instance, HSM_EXIT, NULL);
        current = GetParentStateIndex(instance, current);
        // Only after we've sent exit should we actually exit the state
        instance->_private.currentStateIndex = current;
    }
}

// DownTo must be a child of startAfter
// Sends enter and send entry signals to all states in between startAfter and downTo in the state hierarchy
// The first state to be entered will be the state immediately below startAfter and the last will be downTo
static void PerformEntries(Hsm_st *instance, const HsmStateIndex_t startAfter, const HsmStateIndex_t downTo)
{
    HsmStateIndex_t lastToReceive = startAfter;

    while(lastToReceive != downTo)
    {
        HsmStateIndex_t currentIndex = downTo;
        HsmStateIndex_t previousIndex = downTo;

        // Traverse until currentIndex points at lastToReceive and previousIndex points to the next
        // state to get an entry
        while(currentIndex != lastToReceive)
        {
            previousIndex = currentIndex;
            currentIndex = GetParentStateIndex(instance, currentIndex);
        }

        // Current state should be set before entry is sent
        instance->_private.currentStateIndex = previousIndex;

        if(instance->_private.currentStateIndex == downTo)
        {
            instance->_private.transitionActive = false;
        }

        GetStateFromStateIndex(instance, previousIndex)(instance, HSM_ENTRY, NULL);

        lastToReceive = previousIndex;
    }

    instance->_private.transitionActive = false;
}

void Hsm_Transition(Hsm_st *instance, pfnHsmState_t targetState)
{
    uassert(targetState);
    uassert(false == instance->_private.transitionActive);

    {
        HsmStateIndex_t currentIndex = instance->_private.currentStateIndex;
        HsmStateIndex_t targetIndex = GetStateIndex(instance, targetState);

        pfnHsmState_t currentState = GetStateFromStateIndex(instance, instance->_private.currentStateIndex);

        uassert(targetIndex != INDEX_TOP);

        // Self-transition is a special case -- normally the nearest parent would be self and so no
        // signals would be sent
        if(currentIndex == targetIndex)
        {
            currentState(instance, HSM_EXIT, NULL);
            currentState(instance, HSM_ENTRY, NULL);
        }
        else
        {
            HsmStateIndex_t nearestCommonParent = GetNearestCommonParent(instance, currentIndex, targetIndex);

            instance->_private.transitionActive = true;
            PerformExits(instance, currentIndex, nearestCommonParent);
            PerformEntries(instance, nearestCommonParent, targetIndex);
        }
    }
}

void Hsm_Init(
    Hsm_st *instance,
    const HsmConfiguration_st *configuration,
    pfnHsmState_t initialState)
{
    HsmStateIndex_t initialStateIndex;

    uassert(initialState);

    instance->_private.configuration = configuration;
    initialStateIndex = GetStateIndex(instance, initialState);

    uassert(initialStateIndex != INDEX_TOP);

    VerifyThatParentStatesAreValid(instance);

    instance->_private.transitionActive = true;
    PerformEntries(instance, INDEX_TOP, initialStateIndex);
}
