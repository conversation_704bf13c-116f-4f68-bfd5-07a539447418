# Default platform (x86)
PLATFORM ?= x86

# CMocka paths
CMOCKA_DIR = ../../Tools/cmocka
CMOCKA_LIB = $(CMOCKA_DIR)/lib/libcmocka.a
CMOCKA_INCLUDE = $(CMOCKA_DIR)/include

CORE_DIR = ../../Core
# Compiler and flags based on platform
ifeq ($(PLATFORM),arm)
    CC = arm-none-eabi-gcc
    AR = arm-none-eabi-ar
    CFLAGS = -mcpu=cortex-m0plus -mthumb -Wall -g -O0
    TARGET_DIR = arm_build
		BUILD_TESTS = 0
else
    CC = gcc
    AR = ar
    CFLAGS = -Wall -g -O0 -fPIC
    TARGET_DIR = x86_build
		BUILD_TESTS = 1
endif

INCLUDES = -I. -I$(CORE_DIR)/
# Add CMocka include path for x86 builds with tests
ifeq ($(BUILD_TESTS),1)
    INCLUDES += -I$(CMOCKA_INCLUDE)
endif

# Test related variables - 包含新的测试文件
TEST_SRC = UtilsMacros_Test_CMocka.c
TEST_OBJ = $(TARGET_DIR)/UtilsMacros_Test_CMocka.o
TEST_BIN = $(TARGET_DIR)/test_tinytimer

# Default target
all: $(TARGET_DIR) tests

# Create build directory
$(TARGET_DIR):
	mkdir -p $(TARGET_DIR)

# Compile source files
$(TARGET_DIR)/%.o: $(CORE_DIR)/Timer/%.c | $(TARGET_DIR)
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

$(TARGET_DIR)/%.o: $(CORE_DIR)/DataStructures/LinkedList/%.c | $(TARGET_DIR)
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

$(TARGET_DIR)/%.o: %.c | $(TARGET_DIR)
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@
		
# Compile TinyTimer_Test_CMocka
$(TARGET_DIR)/UtilsMacros_Test_CMocka.o: UtilsMacros_Test_CMocka.c | $(TARGET_DIR)
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# Create static library
$(STATIC_LIB): $(OBJS)
	$(AR) rcs $@ $(OBJS)
	ranlib $@

# Create shared library (x86 only)
$(SHARED_LIB): $(OBJS)
	$(CC) -shared -o $@ $(OBJS)


ifeq ($(BUILD_TESTS),1)
# Build tests (x86 only)
tests: $(CMOCKA_LIB) $(TEST_BIN)

$(TEST_BIN): $(TEST_OBJ) $(STATIC_LIB) $(CMOCKA_LIB)
	$(CC) -o $@ $(TEST_OBJ) $(STATIC_LIB) $(CMOCKA_LIB)

run-tests: $(TEST_BIN)
	./$(TEST_BIN)
else
tests:
	@echo "Tests are only built for x86 platform"
endif

# Clean
clean:
	rm -rf x86_build arm_build

# Clean everything including CMocka
clean-all: clean
	rm -rf $(CMOCKA_DIR)/build $(CMOCKA_DIR)/install

# Build for both platforms
all-platforms: clean
	$(MAKE) PLATFORM=x86
	$(MAKE) PLATFORM=arm

# Test compilation only
test-compile: $(SRCS)
	$(CC) $(CFLAGS) $(INCLUDES) -c $(SRCS) -o /dev/null

.PHONY: all clean clean-all test-compile all-platforms tests run-tests
