/*!
 * @file
 * @brief
 *
 * Copyright xxxx - Confidential - All rights reserved.
 */

#include "TinyTimer.h"
#include "utils.h"

void TinyTimerModule_Init(
    TinyTimerModule_t *instance,
    I_TinyTimeSource_t *timeSource)
{
   instance->_private.timeSource = timeSource;
   instance->_private.lastTicks = TinyTimeSource_GetTicks(timeSource);

   TinyLinkedList_Init(&instance->_private.timers);
}

static void StartTimer(TinyTimerModule_t *instance, TinyTimer_t *timer,
                       TinyTimerTicks_t ticks, TinyTimerCallback_t callback,
                       void *context)
{
   timer->_private.remainingTicks = ticks;
   timer->_private.startTicks = ticks;
   timer->_private.callback = callback;
   timer->_private.context = context;

   TinyLinkedList_Remove(&instance->_private.timers, &timer->_private.node);
   TinyLinkedList_Insert(&instance->_private.timers, &timer->_private.node);
}

void TinyTimerModule_StartOneShot(TinyTimerModule_t *instance, TinyTimer_t *timer,
                                  TinyTimerTicks_t ticks, TinyTimerCallback_t callback,
                                  void *context)
{
   timer->_private.autoReload = false;
   StartTimer(instance, timer, ticks, callback, context);
}

void TinyTimerModule_StartPeriodic(TinyTimerModule_t *instance, TinyTimer_t *timer,
                                   TinyTimerTicks_t ticks, TinyTimerCallback_t callback,
                                   void *context)
{
   timer->_private.autoReload = true;
   StartTimer(instance, timer, ticks, callback, context);
}

void TinyTimerModule_Stop(
    TinyTimerModule_t *instance,
    TinyTimer_t *timer)
{
   TinyLinkedList_Remove(&instance->_private.timers, &timer->_private.node);
}

TinyTimerTicks_t TinyTimerModule_RemainingTicks(
    TinyTimerModule_t *instance,
    TinyTimer_t *timer)
{
   IGNORE(instance);
   return timer->_private.remainingTicks;
}

bool TinyTimerModule_Run(
    TinyTimerModule_t *instance)
{
   TinyTimeSourceTickCount_t currentTicks = TinyTimeSource_GetTicks(instance->_private.timeSource);
   TinyTimerTicks_t elapsedTicks = currentTicks - instance->_private.lastTicks;
   instance->_private.lastTicks = currentTicks;
   bool timerHasBeenCalledBack = false;

   TinyLinkedList_ForEach(&instance->_private.timers, TinyTimer_t, timer, {
      if (timer->_private.remainingTicks > elapsedTicks)
      {
         timer->_private.remainingTicks -= elapsedTicks;
      }
      else
      {
         TinyTimerModule_Stop(instance, timer);
         timer->_private.callback(timer->_private.context, instance);
         timerHasBeenCalledBack = true;

         // Auto-reload for periodic timers
         if (timer->_private.autoReload)
         {
            timer->_private.remainingTicks = timer->_private.startTicks;
            TinyLinkedList_Insert(&instance->_private.timers, &timer->_private.node);
         }
      }
   });

   return timerHasBeenCalledBack;
}
