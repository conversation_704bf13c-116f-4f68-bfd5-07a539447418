/*!
 * @file
 * @brief Tests for utility macros using CMocka
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#include <stdarg.h>
#include <stddef.h>
#include <setjmp.h>
#include <cmocka.h>
#include <stdint.h>
#include <stdbool.h>

#include "utils.h"

// Test functions
static void test_TruncateUnsignedAddition(void **state)
{
    assert_int_equal(21, TRUNCATE_UNSIGNED_ADDITION(1, 20, 255));
    assert_int_equal(15, TRUNCATE_UNSIGNED_ADDITION(1, 20, 15));
    assert_int_equal(21, TRUNCATE_UNSIGNED_ADDITION(20, 1, 255));
    assert_int_equal(15, TRUNCATE_UNSIGNED_ADDITION(20, 1, 15));
}

static void test_TruncateSubtraction(void **state)
{
    assert_int_equal(0, TRUNCATE_UNSIGNED_SUBTRACTION(5, 10));
    assert_int_equal(5, TRUNCATE_UNSIGNED_SUBTRACTION(10, 5));
}

static void test_TruncateSubtractionWithMin(void **state)
{
    assert_int_equal(10, TRUNCATE_UNSIGNED_SUBTRACTION_WITH_MIN(11, 1, 10));
    assert_int_equal(14, TRUNCATE_UNSIGNED_SUBTRACTION_WITH_MIN(22, 8, 10));
    assert_int_equal(10, TRUNCATE_UNSIGNED_SUBTRACTION_WITH_MIN(2, 5, 10));
    assert_int_equal(14, TRUNCATE_UNSIGNED_SUBTRACTION_WITH_MIN(15, 1, 10));
}

static void test_8BitSignedUnsignedValues(void **state)
{
    uint8_t mult = 1;
    uint8_t smaller = 15 * mult;
    uint8_t larger = 120 * mult;
    uint8_t signed_smaller = -127 * mult;
    uint8_t signed_larger = -72 * mult;
    uint8_t same_as_larger = larger;

    assert_int_equal(smaller, MIN(smaller, larger));
    assert_int_equal(larger, MAX(smaller, larger));
    assert_int_equal(signed_smaller, MIN(signed_smaller, signed_larger));
    assert_int_equal(signed_larger, MAX(signed_smaller, signed_larger));
    assert_int_equal(same_as_larger, MAX(smaller, same_as_larger));
}

static void test_16BitSignedUnsignedValues(void **state)
{
    uint16_t mult = 256;
    uint16_t smaller = 15 * mult;
    uint16_t larger = 120 * mult;
    uint16_t signed_smaller = -127 * mult;
    uint16_t signed_larger = -72 * mult;
    uint16_t same_as_larger = larger;

    assert_int_equal(smaller, MIN(smaller, larger));
    assert_int_equal(larger, MAX(smaller, larger));
    assert_int_equal(signed_smaller, MIN(signed_smaller, signed_larger));
    assert_int_equal(signed_larger, MAX(signed_smaller, signed_larger));
    assert_int_equal(same_as_larger, MAX(smaller, same_as_larger));
}

static void test_32BitSignedUnsignedValues(void **state)
{
    uint32_t mult = 256 * 256 * 256;
    uint32_t smaller = 15 * mult;
    uint32_t larger = 120 * mult;
    uint32_t signed_smaller = -127 * mult;
    uint32_t signed_larger = -72 * mult;
    uint32_t same_as_larger = larger;

    assert_int_equal(smaller, MIN(smaller, larger));
    assert_int_equal(larger, MAX(smaller, larger));
    assert_int_equal(signed_smaller, MIN(signed_smaller, signed_larger));
    assert_int_equal(signed_larger, MAX(signed_smaller, signed_larger));
    assert_int_equal(same_as_larger, MAX(smaller, same_as_larger));
}

static void test_64BitSignedUnsignedValues(void **state)
{
    uint64_t mult = (uint64_t)256 * 256 * 256 * 256 * 256 * 256 * 256;
    uint64_t smaller = 15 * mult;
    uint64_t larger = 120 * mult;
    uint64_t signed_smaller = -127 * mult;
    uint64_t signed_larger = -72 * mult;
    uint64_t same_as_larger = larger;

    assert_int_equal(smaller, MIN(smaller, larger));
    assert_int_equal(larger, MAX(smaller, larger));
    assert_int_equal(signed_smaller, MIN(signed_smaller, signed_larger));
    assert_int_equal(signed_larger, MAX(signed_smaller, signed_larger));
    assert_int_equal(same_as_larger, MAX(smaller, same_as_larger));
}

static void test_DivAndRound(void **state)
{
    uint16_t n = 127 * 256;
    uint16_t d = 22 * 256;

    assert_int_equal(-3, DIVIDE_AND_ROUND(-5, 2));
    assert_int_equal(-3, DIVIDE_AND_ROUND(5, -2));
    assert_int_equal(-2, DIVIDE_AND_ROUND(-4, 2));
    assert_int_equal(-2, DIVIDE_AND_ROUND(4, -2));
    assert_int_equal(-48, DIVIDE_AND_ROUND(-238, 5));
    assert_int_equal(-48, DIVIDE_AND_ROUND(238, -5));

    assert_int_equal(3, DIVIDE_AND_ROUND(5, 2));
    assert_int_equal(3, DIVIDE_AND_ROUND(-5, -2));
    assert_int_equal(2, DIVIDE_AND_ROUND(4, 2));
    assert_int_equal(2, DIVIDE_AND_ROUND(-4, -2));
    assert_int_equal(48, DIVIDE_AND_ROUND(238, 5));
    assert_int_equal(48, DIVIDE_AND_ROUND(-238, -5));
    assert_int_equal(((n / d) + 1), DIVIDE_AND_ROUND(-n, -d));
}

static void test_UnsignedDivAndRound(void **state)
{
    uint16_t n = 127 * 256;
    uint16_t d = 22 * 256;

    assert_int_equal(3, UNSIGNED_DIVIDE_AND_ROUND(5, 2));
    assert_int_equal(2, UNSIGNED_DIVIDE_AND_ROUND(4, 2));
    assert_int_equal(48, UNSIGNED_DIVIDE_AND_ROUND(238, 5));
    assert_int_equal(((n / d) + 1), UNSIGNED_DIVIDE_AND_ROUND(n, d));
}

static void test_DivAndRoundUp(void **state)
{
    uint16_t n = 127 * 256;
    uint16_t d = 22 * 256;

    assert_int_equal(-2, DIVIDE_WITH_CEILING(-20, 10));
    assert_int_equal(-2, DIVIDE_WITH_CEILING(20, -10));
    assert_int_equal(-1, DIVIDE_WITH_CEILING(-15, 10));
    assert_int_equal(-1, DIVIDE_WITH_CEILING(15, -10));
    assert_int_equal(-1, DIVIDE_WITH_CEILING(-11, 10));
    assert_int_equal(-1, DIVIDE_WITH_CEILING(11, -10));
    assert_int_equal(-1, DIVIDE_WITH_CEILING(-10, 10));
    assert_int_equal(-1, DIVIDE_WITH_CEILING(10, -10));
    assert_int_equal(0, DIVIDE_WITH_CEILING(-5, 10));
    assert_int_equal(0, DIVIDE_WITH_CEILING(5, -10));
    assert_int_equal(0, DIVIDE_WITH_CEILING(-1, 10));
    assert_int_equal(0, DIVIDE_WITH_CEILING(1, -10));

    assert_int_equal(2, DIVIDE_WITH_CEILING(20, 10));
    assert_int_equal(2, DIVIDE_WITH_CEILING(-20, -10));
    assert_int_equal(2, DIVIDE_WITH_CEILING(15, 10));
    assert_int_equal(2, DIVIDE_WITH_CEILING(-15, -10));
    assert_int_equal(2, DIVIDE_WITH_CEILING(11, 10));
    assert_int_equal(2, DIVIDE_WITH_CEILING(-11, -10));
    assert_int_equal(1, DIVIDE_WITH_CEILING(10, 10));
    assert_int_equal(1, DIVIDE_WITH_CEILING(-10, -10));
    assert_int_equal(1, DIVIDE_WITH_CEILING(5, 10));
    assert_int_equal(1, DIVIDE_WITH_CEILING(-5, -10));
    assert_int_equal(1, DIVIDE_WITH_CEILING(1, 10));
    assert_int_equal(1, DIVIDE_WITH_CEILING(-1, -10));

    assert_int_equal(-3, DIVIDE_WITH_CEILING(-9, 3));
    assert_int_equal(-3, DIVIDE_WITH_CEILING(9, -3));
    assert_int_equal(-2, DIVIDE_WITH_CEILING(-8, 3));
    assert_int_equal(-2, DIVIDE_WITH_CEILING(8, -3));
    assert_int_equal(-2, DIVIDE_WITH_CEILING(-7, 3));
    assert_int_equal(-2, DIVIDE_WITH_CEILING(7, -3));
    assert_int_equal(-2, DIVIDE_WITH_CEILING(-6, 3));
    assert_int_equal(-2, DIVIDE_WITH_CEILING(6, -3));

    assert_int_equal(3, DIVIDE_WITH_CEILING(9, 3));
    assert_int_equal(3, DIVIDE_WITH_CEILING(-9, -3));
    assert_int_equal(3, DIVIDE_WITH_CEILING(8, 3));
    assert_int_equal(3, DIVIDE_WITH_CEILING(-8, -3));
    assert_int_equal(3, DIVIDE_WITH_CEILING(7, 3));
    assert_int_equal(3, DIVIDE_WITH_CEILING(-7, -3));
    assert_int_equal(2, DIVIDE_WITH_CEILING(6, 3));
    assert_int_equal(2, DIVIDE_WITH_CEILING(-6, -3));

    assert_int_equal(((n / d) + 1), DIVIDE_WITH_CEILING(-n, -d));
}

static void test_UnsignedDivAndRoundUp(void **state)
{
    uint16_t n = 127 * 256;
    uint16_t d = 22 * 256;

    assert_int_equal(2, UNSIGNED_DIVIDE_WITH_CEILING(20, 10));
    assert_int_equal(2, UNSIGNED_DIVIDE_WITH_CEILING(15, 10));
    assert_int_equal(2, UNSIGNED_DIVIDE_WITH_CEILING(11, 10));
    assert_int_equal(1, UNSIGNED_DIVIDE_WITH_CEILING(10, 10));

    assert_int_equal(3, UNSIGNED_DIVIDE_WITH_CEILING(9, 3));
    assert_int_equal(3, UNSIGNED_DIVIDE_WITH_CEILING(8, 3));
    assert_int_equal(3, UNSIGNED_DIVIDE_WITH_CEILING(7, 3));
    assert_int_equal(2, UNSIGNED_DIVIDE_WITH_CEILING(6, 3));

    assert_int_equal(((n / d) + 1), UNSIGNED_DIVIDE_WITH_CEILING(n, d));
}

static void test_Between(void **state)
{
    assert_true(BETWEEN(3, 5, 7));
    assert_false(BETWEEN(3, 3, 3));
    assert_false(BETWEEN(3, 3, 4));
    assert_false(BETWEEN(2, 3, 3));
    assert_false(BETWEEN(4, 3, 5));
    assert_false(BETWEEN(2, 3, 2));
}

static void test_InRange(void **state)
{
    assert_true(IN_RANGE(3, 5, 7));
    assert_true(IN_RANGE(3, 3, 3));
    assert_true(IN_RANGE(3, 3, 4));
    assert_true(IN_RANGE(2, 3, 3));
    assert_false(IN_RANGE(4, 3, 5));
    assert_false(IN_RANGE(2, 3, 2));
}

static void test_NumberOfBytes(void **state)
{
    assert_int_equal(1, NUMBER_OF_BYTES(8));
    assert_int_equal(1, NUMBER_OF_BYTES(7));
    assert_int_equal(0, NUMBER_OF_BYTES(0));
    assert_int_equal(2, NUMBER_OF_BYTES(9));
    assert_int_equal(2, NUMBER_OF_BYTES(13));
    assert_int_equal(2, NUMBER_OF_BYTES(16));
}

static void test_BitSetForTables(void **state)
{
    assert_int_equal(0x01, BIT_MASK(0));
    assert_int_equal(0x02, BIT_MASK(1));
    assert_int_equal(0x04, BIT_MASK(2));
    assert_int_equal(0x08, BIT_MASK(3));
    assert_int_equal(0x10, BIT_MASK(4));
    assert_int_equal(0x20, BIT_MASK(5));
    assert_int_equal(0x40, BIT_MASK(6));
    assert_int_equal(0x80, BIT_MASK(7));
    assert_int_equal(0x82, BIT_MASK(7) | BIT_MASK(1));
}

static void test_Clamp(void **state)
{
    assert_int_equal(5, CLAMP(18, 1, 5));
    assert_int_equal(18, CLAMP(18, 1, 25));
    assert_int_equal(19, CLAMP(18, 19, 25));
}

static void test_BitWrite(void **state)
{
    uint8_t test = 0x0;
    BIT_WRITE(test, 0, 1);
    assert_int_equal(0x1, test);
    BIT_WRITE(test, 7, 1);
    assert_int_equal(0x81, test);
    BIT_WRITE(test, 3, 1);
    assert_int_equal(0x89, test);

    BIT_WRITE(test, 0, 0);
    assert_int_equal(0x88, test);
    BIT_WRITE(test, 3, 0);
    assert_int_equal(0x80, test);
    BIT_WRITE(test, 7, 0);
    assert_int_equal(0x0, test);
}

static void test_BitmapWrite(void **state)
{
    struct
    {
        uint8_t byte[2];
    } test = { 0 };

    BITMAP_WRITE(test, 0, 1);
    assert_int_equal(0x1, test.byte[0]);
    assert_int_equal(0x0, test.byte[1]);
    BITMAP_WRITE(test, 7, 1);
    assert_int_equal(0x81, test.byte[0]);
    assert_int_equal(0x00, test.byte[1]);
    BITMAP_WRITE(test, 10, 1);
    assert_int_equal(0x81, test.byte[0]);
    assert_int_equal(0x04, test.byte[1]);

    BITMAP_WRITE(test, 0, 0);
    assert_int_equal(0x80, test.byte[0]);
    assert_int_equal(0x04, test.byte[1]);
    BITMAP_WRITE(test, 7, 0);
    assert_int_equal(0x00, test.byte[0]);
    assert_int_equal(0x04, test.byte[1]);
    BITMAP_WRITE(test, 10, 0);
    assert_int_equal(0x0, test.byte[0]);
    assert_int_equal(0x0, test.byte[1]);
}

int main(void)
{
    const struct CMUnitTest tests[] = {
        cmocka_unit_test(test_TruncateUnsignedAddition),
        cmocka_unit_test(test_TruncateSubtraction),
        cmocka_unit_test(test_TruncateSubtractionWithMin),
        cmocka_unit_test(test_8BitSignedUnsignedValues),
        cmocka_unit_test(test_16BitSignedUnsignedValues),
        cmocka_unit_test(test_32BitSignedUnsignedValues),
        cmocka_unit_test(test_64BitSignedUnsignedValues),
        cmocka_unit_test(test_DivAndRound),
        cmocka_unit_test(test_UnsignedDivAndRound),
        cmocka_unit_test(test_DivAndRoundUp),
        cmocka_unit_test(test_UnsignedDivAndRoundUp),
        cmocka_unit_test(test_Between),
        cmocka_unit_test(test_InRange),
        cmocka_unit_test(test_NumberOfBytes),
        cmocka_unit_test(test_BitSetForTables),
        cmocka_unit_test(test_Clamp),
        cmocka_unit_test(test_BitWrite),
        cmocka_unit_test(test_BitmapWrite),
    };

    return cmocka_run_group_tests(tests, NULL, NULL);
}