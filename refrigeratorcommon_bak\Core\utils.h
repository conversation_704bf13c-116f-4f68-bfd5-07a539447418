/*!
 * @file
 * @brief Miscellaneous utilities and macros.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#ifndef UTILS_H
#define UTILS_H

#include <stddef.h>
#include <stdint.h>
#include <string.h>

// Contortions like these are necessary to get the arguments of concatenate to
// be macro expanded before the concatenation occurs
#define _MACRO_SAFE_CONCATENATE(a, b) a##b
#define MACRO_SAFE_CONCATENATE(a, b) _MACRO_SAFE_CONCATENATE(a, b)
#define CONCAT MACRO_SAFE_CONCATENATE

// Used to perform a compile-time check that can't be done with the
// pre-processor (ie: sizeof checks)
#define STATIC_ASSERT(cond) \
    typedef int MACRO_SAFE_CONCATENATE(assert, __LINE__)[(cond) ? 1 : -1]

#define unless(cond) if(!(cond))

#define NUM_ELEMENTS(array) (sizeof(array) / sizeof(array[0]))
#define ELEMENT_COUNT NUM_ELEMENTS
#define ELEMENT_SIZE(array) (sizeof(array[0]))

#ifndef OFFSET_OF
#define OFFSET_OF offsetof
#endif

#define MEMBER_SIZE(container_type, field_name) \
    sizeof(((container_type *)0)->field_name)

#define IS_SIGNED(integer_type) (((integer_type)-1) < 0)

// void * cast is necessary to avoid spurious alignment warnings
#define CONTAINER_OF(container_type, field_name, pointer_to_field) \
    (container_type *)((void *)((char *)pointer_to_field -         \
        OFFSET_OF(container_type, field_name)))

#define IGNORE_ARG(arg) (void)(arg)
#define IGNORE(arg) IGNORE_ARG(arg)

#define REINTERPRET(new_name, cast_me, new_type) \
    new_type new_name = (new_type)cast_me

#define REINTERPRET_CAST(new_name, cast_me, new_type) \
    REINTERPRET(new_name, cast_me, new_type)

#ifndef MIN
#define MIN(a, b) (((a) < (b)) ? (a) : (b))
#endif

#ifndef MAX
#define MAX(a, b) (((a) > (b)) ? (a) : (b))
#endif

#define CLAMP(value, min, max) MAX(MIN((value), (max)), (min))

#define DIVIDE_AND_ROUND(n, d) \
    (((n) + (((n) >= 0) ^ ((d) >= 0) ? -1 : 1) * ((d) >> 1)) / (d))
#define UNSIGNED_DIVIDE_AND_ROUND(n, d) (((n) + ((d) >> 1)) / (d))

#define DIVIDE_WITH_CEILING(n, d) \
    ((n) / (d) + (((n) % (d) != 0) ? !(((n) > 0) ^ ((d) > 0)) : 0))

#define TRUNCATE_UNSIGNED_ADDITION(a, b, max) \
    ((((a) / (max)) || ((b) / (max))) ? (max) \
                                      : (((max - a) > (b)) ? (a + b) : (max)))
#define TRUNCATE_UNSIGNED_SUBTRACTION(a, b) (((a) > (b)) ? (a - b) : (0))
#define TRUNCATE_UNSIGNED_SUBTRACTION_WITH_MIN(a, b, min) \
    (((a) > ((b) + min)) ? (a - b) : (min))

#define INSTANCE_DATA (instance->_private)

#define IN_RANGE(low, value, high) (((low) <= (value)) && ((value) <= (high)))
#define BETWEEN(low, value, high) (((low) < (value)) && ((value) < (high)))

#define BIT_SET(byte, bit_index) \
    (byte) |= 1 << (bit_index)

#define BIT_CLEAR(byte, bit_index) \
    (byte) &= ~(1 << (bit_index))

#define BIT_STATE(byte, bit_index) \
    !!((byte) & (1 << (bit_index)))

#define BITMAP_SET(bitmap, bit_index) \
    BIT_SET((((uint8_t *)&bitmap)[(bit_index) / 8]), (bit_index) % 8)

#define BITMAP_CLEAR(bitmap, bit_index) \
    BIT_CLEAR((((uint8_t *)&bitmap)[(bit_index) / 8]), (bit_index) % 8)

#define BITMAP_STATE(bitmap, bit_index) \
    BIT_STATE((((const uint8_t *)&bitmap)[(bit_index) / 8]), (bit_index) % 8)

#define BITMAP_WRITE(bitmap, bit_index, state) \
    if(state)                                  \
        BITMAP_SET(bitmap, bit_index);         \
    else                                       \
        BITMAP_CLEAR(bitmap, bit_index)

#define NUMBER_OF_BYTES(bits) ((bits / 8) + !!(bits % 8))

#define BIT_MASK(bit_index) (1 << (bit_index))

#define MEMORY_IS_EQUAL(a, b) (0 == memcmp(&a, &b, sizeof(a)))
typedef struct
{
    uint8_t u8_LSB;
    uint8_t u8_MSB;
} LsbMsbBytes_st;

typedef union
{
    uint16_t u16_Word;
    LsbMsbBytes_st sByte;
} MyWord_st;

#define BIT_INDEX_0 (uint8_t)0
#define BIT_INDEX_1 (uint8_t)1
#define BIT_INDEX_2 (uint8_t)2
#define BIT_INDEX_3 (uint8_t)3
#define BIT_INDEX_4 (uint8_t)4
#define BIT_INDEX_5 (uint8_t)5
#define BIT_INDEX_6 (uint8_t)6
#define BIT_INDEX_7 (uint8_t)7

#define YES (1)
#define NO (0)
#define OFF (0)
#define ON (1)
#define SUCCESS (0)
#define FAIL (1)
#define CORE_SET (1)
#define CORE_CLEAR (0)
#define GET_U16_HIGHBYTE(U16_TYPE) ((uint8_t)((U16_TYPE) >> 8))
#define GET_U16_LOWBYTE(U16_TYPE) ((uint8_t)((U16_TYPE) & 0x00FF))

#define MAX_I8 ((int8_t)127)
#define MAX_INT8 (MAX_I8)

#define MIN_I8 ((int8_t)(-128))
#define MIN_INT8 (MIN_I8)

#define MAX_U8 ((uint8_t)255)
#define MAX_UINT8 (MAX_U8)

#define MIN_U8 ((uint8_t)0)
#define MIN_UINT8 (MIN_U8)

#define MAX_I16 ((int16_t)32767)
#define MAX_INT16 (MAX_I16)

#define MIN_I16 ((int16_t)(-32768))
#define MIN_INT16 (MIN_I16)

#define MAX_U16 ((uint16_t)65535)
#define MAX_UINT16 (MAX_U16)

#define MIN_U16 ((uint16_t)0)
#define MIN_UINT16 (MIN_U16)

#define MAX_I32 ((int32_t)2147483647)
#define MAX_INT32 (MAX_I32)

#define MIN_I32 ((int32_t)(-2147483648))
#define MIN_INT32 (MIN_I32)

#define MAX_U32 ((uint32_t)4294967295)
#define MAX_UINT32 (MAX_U32)

#define MIN_U32 ((uint32_t)0)
#define MIN_UINT32 (MIN_U32)

#define ZERO (0)
#define ONE (1)
#define TWO (2)
#define THREE (3)
#define FOUR (4)
#define FIVE (5)
#define SIX (6)
#define SEVEN (7)
#define EIGHT (8)
#define NINE (9)
#define TEN (10)
#define ELEVEN (11)
#define TWELVE (12)

#endif
