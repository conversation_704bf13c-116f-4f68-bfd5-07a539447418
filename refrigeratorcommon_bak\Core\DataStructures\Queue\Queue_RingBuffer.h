/*!
 * @file
 * @brief An implementation of a queue that uses a ring buffer
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved.
 */

#ifndef QUEUE_RINGBUFFER_H
#define QUEUE_RINGBUFFER_H

#include "I_Queue.h"
#include "RingBuffer.h"

typedef struct
{
    I_Queue_st interface;

    struct
    {
        uint16_t numberOfElements;
        RingBuffer_st ringBuffer;
    } _private;
} Queue_RingBuffer_st;

/*!
 * Initializes the queue
 * @param instance The instance
 * @param storage The space that the queue can use
 * @param storageSize The number of bytes in the allotted storage
 */
void Queue_RingBuffer_Init(Queue_RingBuffer_st *instance, void *storage, size_t storageSize);

#endif
