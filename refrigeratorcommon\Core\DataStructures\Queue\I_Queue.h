/*!
 * @file
 * @brief Defines the interface for a generic queue
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved.
 */

#ifndef I_QUEUE_H
#define I_QUEUE_H

#include <stdbool.h>
#include <stdint.h>

typedef uint16_t QueueElementSize_t;

struct I_Queue_Api_st;

typedef struct
{
    const struct I_Queue_Api_st *api;
} I_Queue_st;

typedef struct I_Queue_Api_st
{
    /*!
     * Add an element to the queue.
     * @pre The count of elements in the queue < UINT16_MAX
     * @param instance The queue
     * @param element The element to enqueue
     * @param size The size of the element
     * @return true if the element was successfully enqueued, false otherwise
     */
    bool (*Enqueue)(I_Queue_st *instance, const void *element, QueueElementSize_t size);

    /*!
     * Remove an element from the queue.
     * @pre the count of elements queued is > 0
     * @param instance The queue
     * @param element Storage for the dequeued element
     * @param size Storage for the size of the dequeued packet
     */
    void (*Dequeue)(I_Queue_st *instance, void *element, QueueElementSize_t *size);

    /*!
     * Similar to Dequeue but the element is not retrieved.
     * @pre the count of elements queued is > 0
     * @param instance The queue
     */
    void (*Discard)(I_Queue_st *instance);

    /*!
     * Peek an element. An element can be viewed from anywhere in the queue but is
     * not dequeued.
     * @pre 0 <= index < The count of elements in the queue
     * @param instance The queue.
     * @param element Storage for the dequeued element
     * @param size Storage for the size of the dequeued packet
     * @param index The number of the element that is desired to be peeked
     */
    void (*Peek)(I_Queue_st *instance, void *element, QueueElementSize_t *size, uint16_t index);

    /*!
     * Peek part of an element. Only copies out up to the specified number of
     * bytes.
     * @pre 0 <= index < The count of elements in the queue
     * @param instance The queue
     * @param element Storage for the dequeued element
     * @param size Maximum number of bytes of the part that will be peeked
     * @param index The number of the element that is desired to be peeked
     */
    void (*PeekPartial)(I_Queue_st *instance, void *element, QueueElementSize_t sizeLimit, uint16_t index);

    /*!
     * Peek part of an element. Only copies out up to the specified number of
     * bytes.
     * @pre 0 <= index < The count of elements in the queue
     * @param instance The queue
     * @param size Size of peeked element
     * @param index The number of the element that is desired to be peeked
     */
    void (*PeekSize)(I_Queue_st *instance, QueueElementSize_t *size, uint16_t index);

    /*!
     * Get the number of elements in the queue
     * @param instance The queue
     * @return The number of elements currently queued
     */
    uint16_t (*Count)(I_Queue_st *instance);
} I_Queue_Api_st;

#define Queue_Enqueue(instance, element, size) \
    (instance)->api->Enqueue((instance), (element), (size))

#define Queue_Dequeue(instance, element, size) \
    (instance)->api->Dequeue((instance), (element), (size))

#define Queue_Discard(instance) (instance)->api->Discard((instance))

#define Queue_Peek(instance, element, size, index) \
    (instance)->api->Peek((instance), (element), (size), (index))

#define Queue_PeekPartial(instance, element, sizeLimit, index) \
    (instance)->api->PeekPartial((instance), (element), (sizeLimit), (index))

#define Queue_PeekSize(instance, size, index) \
    (instance)->api->PeekSize((instance), (size), (index))

#define Queue_Count(instance) (instance)->api->Count((instance))

#endif
