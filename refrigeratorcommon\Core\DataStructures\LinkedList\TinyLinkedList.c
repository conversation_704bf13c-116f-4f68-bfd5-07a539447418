/*!
 * @file
 * @brief
 *
 * Copyright xxxx - Confidential - All rights reserved
 */

#include <stddef.h>
#include "TinyLinkedList.h"

void TinyLinkedList_Init(TinyLinkedList_t *instance)
{
   instance->_private.head._private.next = &instance->_private.head;
}

void TinyLinkedList_Insert(TinyLinkedList_t *instance, TinyLinkedListNode_t *node)
{
   node->_private.next = instance->_private.head._private.next;
   instance->_private.head._private.next = node;
}

void TinyLinkedList_Remove(TinyLinkedList_t *instance, TinyLinkedListNode_t *node)
{
   TinyLinkedListNode_t *current = &instance->_private.head;

   while (current->_private.next != &instance->_private.head)
   {
      if (current->_private.next == node)
      {
         current->_private.next = node->_private.next;
         break;
      }

      current = current->_private.next;
   }
}

void TinyLinkedListIterator_Init(TinyLinkedListIterator_t *instance, TinyLinkedList_t *list)
{
   instance->_private.current = list->_private.head._private.next;
}

TinyLinkedListNode_t *TinyLinkedListIterator_Next(TinyLinkedListIterator_t *instance, TinyLinkedList_t *list)
{
   if (instance->_private.current == &list->_private.head)
   {
      return NULL;
   }
   else
   {
      TinyLinkedListNode_t *item = instance->_private.current;
      instance->_private.current = instance->_private.current->_private.next;
      return item;
   }
}
