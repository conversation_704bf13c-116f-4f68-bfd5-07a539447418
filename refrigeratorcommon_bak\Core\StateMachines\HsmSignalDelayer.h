/*!
 * @file
 * @brief Definitions for an HsmSignalDelayer
 *
 * This component takes an HSM, a signal, and a time source, and delays the
 * signal publication until after the completion of the code calling this
 * component's SendDelayedSignal method.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#ifndef HSMSIGNALDELAYER
#define HSMSIGNALDELAYER

#include "Hsm.h"
#include "Timer.h"

typedef struct
{
    struct
    {
        struct
        {
            TimerModule_st *timerModule;
            Timer_st delayTimer;
        } delayConfig;

        struct
        {
            Hsm_st *hsm;
            HsmSignal_t signal;
        } delayedSignalContext;
    } _private;
} HsmSignalDelayer_st;

/*!
 * Initialize an HSM signal delayer.
 * @pre references != NULL
 * @param instance The HSM signal delayer.
 * @param hsm The HSM that the signal will be published to.
 * @param timerModule Timer module used to manage the delay.
 */
void HsmSignalDelayer_Init(HsmSignalDelayer_st *instance, Hsm_st *hsm, TimerModule_st *timerModule);

/*!
 * Queue a signal for delayed send.
 * @pre instance != NULL
 * @pre Cannot call while a signal is already pending delivery.
 * @param instance The HSM signal delayer.
 * @param signal The signal to send.
 */
void HsmSignalDelayer_SendDelayedSignal(HsmSignalDelayer_st *instance,
    const HsmSignal_t signal);

#endif
