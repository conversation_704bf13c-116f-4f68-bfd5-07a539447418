/*!
 * @file
 * @brief Statically allocated linked list.  Nodes are allocated by clients.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#include <stddef.h>
#include <stdbool.h>
#include "LinkedList.h"
#include "uassert.h"

void LinkedList_Init(LinkedList_st *list)
{
    list->_private.head = NULL;
}

size_t LinkedList_Count(const LinkedList_st *list)
{
    LinkedListNode_st *current = list->_private.head;
    size_t count = 0;

    while(current)
    {
        current = current->_private.next;
        count++;
    }

    return count;
}

LinkedListNode_st *LinkedList_At(const LinkedList_st *list, const size_t index)
{
    LinkedListNode_st *current = list->_private.head;
    uassert(current);

    for(size_t i = 0; i < index; i++)
    {
        current = current->_private.next;
        uassert(current);
    }

    return current;
}

size_t LinkedList_IndexOf(const LinkedList_st *list, const LinkedListNode_st *node)
{
    LinkedListNode_st *current = list->_private.head;
    size_t index = 0;

    while(current)
    {
        if(current == node)
        {
            return index;
        }

        current = current->_private.next;
        index++;
    }

    uassert(!"node not in the list!");
    return 0;
}

bool LinkedList_Contains(const LinkedList_st *list, const LinkedListNode_st *node)
{
    LinkedListNode_st *current = list->_private.head;

    if(current == node)
    {
        return true;
    }

    while(current)
    {
        current = (current->_private.next);

        if(current == node)
        {
            return true;
        }
    }

    return false;
}

void LinkedList_PushFront(LinkedList_st *list, LinkedListNode_st *node)
{
    uassert(!LinkedList_Contains(list, node));

    node->_private.next = list->_private.head;
    list->_private.head = node;
}

void LinkedList_PushBack(LinkedList_st *list, LinkedListNode_st *node)
{
    uassert(!LinkedList_Contains(list, node));

    LinkedListNode_st *current = list->_private.head;

    node->_private.next = NULL;

    if(current)
    {
        while(current->_private.next)
        {
            current = current->_private.next;
        }

        current->_private.next = node;
    }
    else
    {
        list->_private.head = node;
    }
}

void LinkedList_Insert(LinkedList_st *list, LinkedListNode_st *node, size_t index)
{
    uassert(!LinkedList_Contains(list, node));

    if(index > 0)
    {
        LinkedListNode_st *before = LinkedList_At(list, index - 1);
        node->_private.next = before->_private.next;
        before->_private.next = node;
    }
    else
    {
        LinkedList_PushFront(list, node);
    }
}

LinkedListNode_st *LinkedList_PopFront(LinkedList_st *list)
{
    uassert(list->_private.head);

    LinkedListNode_st *popped = list->_private.head;
    list->_private.head = list->_private.head->_private.next;

    return popped;
}

LinkedListNode_st *LinkedList_PopBack(LinkedList_st *list)
{
    uassert(list->_private.head);

    LinkedListNode_st *popped;
    LinkedListNode_st *current = list->_private.head;

    if(current->_private.next)
    {
        while(current->_private.next->_private.next)
        {
            current = current->_private.next;
        }

        popped = current->_private.next;
        current->_private.next = NULL;
    }
    else
    {
        popped = list->_private.head;
        list->_private.head = NULL;
    }

    return popped;
}

void LinkedList_RemoveAt(LinkedList_st *list, const size_t index)
{
    LinkedListNode_st *previous = NULL;
    LinkedListNode_st *current = list->_private.head;

    for(size_t i = 0; i < index; i++)
    {
        uassert(current);
        previous = current;
        current = current->_private.next;
    }

    uassert(current);

    if(previous)
    {
        previous->_private.next = current->_private.next;
    }
    else
    {
        list->_private.head = current->_private.next;
    }
}

void LinkedList_Remove(LinkedList_st *list, LinkedListNode_st *node)
{
    if(list->_private.head == node)
    {
        list->_private.head = node->_private.next;
    }
    else
    {
        LinkedListNode_st *current = list->_private.head;

        while(current)
        {
            if(current->_private.next == node)
            {
                current->_private.next = node->_private.next;
                break;
            }

            current = current->_private.next;
        }
    }
}

void LinkedListIterator_Init(LinkedListIterator_st *instance, LinkedList_st *list)
{
    instance->_private.current = list->_private.head;
}

LinkedListNode_st *LinkedListIterator_Next(LinkedListIterator_st *instance)
{
    if(!instance->_private.current)
    {
        return NULL;
    }

    LinkedListNode_st *item = instance->_private.current;
    instance->_private.current = instance->_private.current->_private.next;
    return item;
}
