/*!
 * @file
 * @brief Implementation of the Queue_RingBuffer module
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved.
 */

#include "Queue_RingBuffer.h"
#include "utils.h"
#include "uassert.h"

static void StoreSizeOfNewElement(Queue_RingBuffer_st *instance, QueueElementSize_t sizeOfElement)
{
    uint8_t *byte = (uint8_t *)&sizeOfElement;
    for(QueueElementSize_t index = 0; index < sizeof(QueueElementSize_t); index++)
    {
        RingBuffer_Add(&instance->_private.ringBuffer, byte);
        byte++;
    }
}

static void StoreNewElement(Queue_RingBuffer_st *instance, const void *elementPlaceHolder, QueueElementSize_t sizeOfElement)
{
    const uint8_t *byte = elementPlaceHolder;
    for(QueueElementSize_t index = 0; index < sizeOfElement; index++)
    {
        RingBuffer_Add(&instance->_private.ringBuffer, byte);
        byte++;
    }
}

static void RemoveSizeOfNextElement(Queue_RingBuffer_st *instance, QueueElementSize_t *sizePlaceHolder)
{
    uint8_t *byte = (uint8_t *)sizePlaceHolder;
    for(QueueElementSize_t index = 0; index < sizeof(QueueElementSize_t); index++)
    {
        RingBuffer_Remove(&instance->_private.ringBuffer, byte);
        byte++;
    }
}

static void RemoveNextElement(Queue_RingBuffer_st *instance, void *removedElementStorage, QueueElementSize_t sizeOfElement)
{
    uint8_t *byte = removedElementStorage;
    for(QueueElementSize_t index = 0; index < sizeOfElement; index++)
    {
        RingBuffer_Remove(&instance->_private.ringBuffer, byte);
        byte++;
    }
}

static void DiscardNextElement(Queue_RingBuffer_st *instance, QueueElementSize_t sizeOfElement)
{
    for(QueueElementSize_t index = 0; index < sizeOfElement; index++)
    {
        RingBuffer_Discard(&instance->_private.ringBuffer);
    }
}

static void PeekSizeOfElementFromALocation(Queue_RingBuffer_st *instance, QueueElementSize_t *placeHolder, uint16_t location)
{
    uint8_t *byte = (uint8_t *)placeHolder;
    for(uint16_t index = location; index < location + sizeof(QueueElementSize_t); index++)
    {
        RingBuffer_At(&instance->_private.ringBuffer, byte, index);
        byte++;
    }
}

static uint16_t TheIndexOfTheRingBufferInWhichThisElementIsStored(Queue_RingBuffer_st *instance, uint16_t desiredElementIndex)
{
    QueueElementSize_t sizeOfElement;
    uint16_t location = 0;
    for(uint16_t currentElementIndex = 0; currentElementIndex < desiredElementIndex; currentElementIndex++)
    {
        PeekSizeOfElementFromALocation(instance, &sizeOfElement, location);
        location += sizeOfElement + sizeof(QueueElementSize_t);
    }
    return location;
}

static void PeekAnElementFromALocation(Queue_RingBuffer_st *instance, void *peekedElementStorage, QueueElementSize_t sizeOfElement, uint16_t location)
{
    uint8_t *byte = peekedElementStorage;
    for(QueueElementSize_t index = location; index < location + sizeOfElement; index++)
    {
        RingBuffer_At(&instance->_private.ringBuffer, byte, index);
        byte++;
    }
}

static bool Enqueue(I_Queue_st *_instance, const void *element, QueueElementSize_t sizeOfElement)
{
    REINTERPRET(instance, _instance, Queue_RingBuffer_st *);

    size_t theSpaceThatThisElementWillTakeUp = sizeOfElement + sizeof(QueueElementSize_t);
    size_t theSpaceLeftInTheRingBuffer = RingBuffer_Capacity(&instance->_private.ringBuffer) - RingBuffer_Count(&instance->_private.ringBuffer);

    if(theSpaceThatThisElementWillTakeUp <= theSpaceLeftInTheRingBuffer)
    {
        StoreSizeOfNewElement(instance, sizeOfElement);
        StoreNewElement(instance, element, sizeOfElement);
        instance->_private.numberOfElements++;
        uassert(instance->_private.numberOfElements < UINT16_MAX);
        return true;
    }
    else
    {
        return false;
    }
}

static void Dequeue(I_Queue_st *_instance, void *element, QueueElementSize_t *sizeStorage)
{
    REINTERPRET(instance, _instance, Queue_RingBuffer_st *);

    uassert(instance->_private.numberOfElements > 0);

    RemoveSizeOfNextElement(instance, sizeStorage);
    RemoveNextElement(instance, element, *sizeStorage);
    instance->_private.numberOfElements--;
}

static void Discard(I_Queue_st *_instance)
{
    REINTERPRET(instance, _instance, Queue_RingBuffer_st *);

    uassert(instance->_private.numberOfElements > 0);

    QueueElementSize_t sizeStorage;
    RemoveSizeOfNextElement(instance, &sizeStorage);
    DiscardNextElement(instance, sizeStorage);
    instance->_private.numberOfElements--;
}

static void Peek(I_Queue_st *_instance, void *element, QueueElementSize_t *sizeStorage, uint16_t elementIndexNumber)
{
    REINTERPRET(instance, _instance, Queue_RingBuffer_st *);

    uassert(elementIndexNumber < instance->_private.numberOfElements);

    uint16_t index = TheIndexOfTheRingBufferInWhichThisElementIsStored(instance, elementIndexNumber);
    PeekSizeOfElementFromALocation(instance, sizeStorage, index);
    PeekAnElementFromALocation(instance, element, *sizeStorage, index + sizeof(QueueElementSize_t));
}

static void PeekPartial(I_Queue_st *_instance, void *element, QueueElementSize_t size, uint16_t elementIndexNumber)
{
    REINTERPRET(instance, _instance, Queue_RingBuffer_st *);

    uassert(elementIndexNumber < instance->_private.numberOfElements);

    uint16_t index = TheIndexOfTheRingBufferInWhichThisElementIsStored(instance, elementIndexNumber);
    PeekAnElementFromALocation(instance, element, size, index + sizeof(QueueElementSize_t));
}

static void PeekSize(I_Queue_st *_instance, QueueElementSize_t *sizeStorage, uint16_t elementIndexNumber)
{
    REINTERPRET(instance, _instance, Queue_RingBuffer_st *);

    uassert(elementIndexNumber < instance->_private.numberOfElements);

    uint16_t index = TheIndexOfTheRingBufferInWhichThisElementIsStored(instance, elementIndexNumber);
    PeekSizeOfElementFromALocation(instance, sizeStorage, index);
}

static uint16_t Count(I_Queue_st *_instance)
{
    REINTERPRET(instance, _instance, Queue_RingBuffer_st *);
    return instance->_private.numberOfElements;
}

static const I_Queue_Api_st api = {
    .Enqueue = Enqueue,
    .Dequeue = Dequeue,
    .Discard = Discard,
    .Peek = Peek,
    .PeekPartial = PeekPartial,
    .PeekSize = PeekSize,
    .Count = Count
};

void Queue_RingBuffer_Init(
    Queue_RingBuffer_st *instance,
    void *storage,
    size_t storageSize)
{
    instance->interface.api = &api;
    instance->_private.numberOfElements = 0;
    RingBuffer_Init(&instance->_private.ringBuffer, storage, storageSize, sizeof(uint8_t));
}
