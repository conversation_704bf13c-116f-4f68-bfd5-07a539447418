# Makefile for StepperMotorLib (multi-platform)

# Default platform (x86)
PLATFORM ?= x86

# CMocka paths
CMOCKA_DIR = ../../Tools/cmocka
CMOCKA_LIB = $(CMOCKA_DIR)/lib/libcmocka.a
CMOCKA_INCLUDE = $(CMOCKA_DIR)/include

CORE_DIR = ../../Core
# Compiler and flags based on platform
ifeq ($(PLATFORM),arm)
    CC = arm-none-eabi-gcc
    AR = arm-none-eabi-ar
    CFLAGS = -mcpu=cortex-m0plus -mthumb -Wall -g -O0
    TARGET_DIR = arm_build
		BUILD_TESTS = 0
else
    CC = gcc
    AR = ar
    CFLAGS = -Wall -g -O0 -fPIC
    TARGET_DIR = x86_build
		BUILD_TESTS = 1
endif

INCLUDES = -I. -I$(CORE_DIR)/ -I$(CORE_DIR)/Timer -I$(CORE_DIR)/DataStructures/LinkedList -I../../../Code/Bsp
# Add CMocka include path for x86 builds with tests
ifeq ($(BUILD_TESTS),1)
    INCLUDES += -I$(CMOCKA_INCLUDE)
endif

# Source files
SRCS = $(CORE_DIR)/Timer/TinyTimer.c \
       $(CORE_DIR)/DataStructures/LinkedList/TinyLinkedList.c
TEST_SRCS = TinyTimeSource_TestDouble.c

TIMER_OBJS = $(patsubst $(CORE_DIR)/Timer/%.c,$(TARGET_DIR)/%.o,$(filter $(CORE_DIR)/Timer/%.c,$(SRCS)))
LINKEDLIST_OBJS = $(patsubst $(CORE_DIR)/DataStructures/LinkedList/%.c,$(TARGET_DIR)/%.o,$(filter $(CORE_DIR)/DataStructures/LinkedList/%.c,$(SRCS)))
TEST_DOUBLE_OBJS = $(patsubst %.c,$(TARGET_DIR)/%.o,$(TEST_SRCS))
OBJS = $(TIMER_OBJS) $(LINKEDLIST_OBJS) $(TEST_DOUBLE_OBJS)

STATIC_LIB = $(TARGET_DIR)/libtinytimer.a

# Add shared library for x86 only
ifeq ($(PLATFORM),x86)
    SHARED_LIB = $(TARGET_DIR)/libtinytimer.so
    ALL_TARGETS = $(SHARED_LIB)
else
    ALL_TARGETS = $(STATIC_LIB)
endif

# Test related variables - 包含新的测试文件
TEST_SRC = TinyTimer_Test_CMocka.c
TEST_OBJ = $(TARGET_DIR)/TinyTimer_Test_CMocka.o
TEST_BIN = $(TARGET_DIR)/test_tinytimer

# Default target
all: $(TARGET_DIR) tests

# Create build directory
$(TARGET_DIR):
	mkdir -p $(TARGET_DIR)

# Compile source files
$(TARGET_DIR)/%.o: $(CORE_DIR)/Timer/%.c | $(TARGET_DIR)
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

$(TARGET_DIR)/%.o: $(CORE_DIR)/DataStructures/LinkedList/%.c | $(TARGET_DIR)
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

$(TARGET_DIR)/%.o: %.c | $(TARGET_DIR)
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@
		
# Compile TinyTimer_Test_CMocka
$(TARGET_DIR)/TinyTimer_Test_CMocka.o: TinyTimer_Test_CMocka.c | $(TARGET_DIR)
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

# Create static library
$(STATIC_LIB): $(OBJS)
	$(AR) rcs $@ $(OBJS)
	ranlib $@

# Create shared library (x86 only)
$(SHARED_LIB): $(OBJS)
	$(CC) -shared -o $@ $(OBJS)


ifeq ($(BUILD_TESTS),1)
# Build tests (x86 only)
tests: $(CMOCKA_LIB) $(TEST_BIN)

$(TEST_BIN): $(TEST_OBJ) $(STATIC_LIB) $(CMOCKA_LIB)
	$(CC) -o $@ $(TEST_OBJ) $(STATIC_LIB) $(CMOCKA_LIB)

run-tests: $(TEST_BIN)
	./$(TEST_BIN)
else
tests:
	@echo "Tests are only built for x86 platform"
endif

# Clean
clean:
	rm -rf x86_build arm_build

# Clean everything including CMocka
clean-all: clean
	rm -rf $(CMOCKA_DIR)/build $(CMOCKA_DIR)/install

# Build for both platforms
all-platforms: clean
	$(MAKE) PLATFORM=x86
	$(MAKE) PLATFORM=arm

# Test compilation only
test-compile: $(SRCS)
	$(CC) $(CFLAGS) $(INCLUDES) -c $(SRCS) -o /dev/null

.PHONY: all clean clean-all test-compile all-platforms tests run-tests
