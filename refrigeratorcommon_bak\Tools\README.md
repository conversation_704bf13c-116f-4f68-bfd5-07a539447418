# Tools目录说明

本目录包含项目构建所需的第三方工具和库。

## 目录结构

```text
Tools/
├── README.md                     # 本说明文件
└── cmocka/                      # CMocka单元测试框架
    ├── include/                 # CMocka头文件
    │   ├── cmocka.h
    │   └── cmocka_pbc.h
    └── lib/                     # CMocka静态库
        └── libcmocka.a
```

## 组件说明

### CMocka测试框架

- **版本**: CMocka 1.1.7
- **类型**: 静态库
- **用途**: C语言单元测试框架
- **特性**:
  - 轻量级的C单元测试框架
  - 支持模拟函数(Mock Functions)
  - 内存泄漏检测
  - 异常处理测试
  - 跨平台支持

#### 主要功能

- **断言宏**: `assert_int_equal()`, `assert_string_equal()`, `assert_null()` 等
- **测试组织**: `cmocka_unit_test()`, `cmocka_run_group_tests()` 等
- **模拟支持**: `will_return()`, `expect_value()`, `mock()` 等
- **内存检查**: 自动检测内存泄漏和缓冲区溢出

## 使用方法

CMocka框架通过makefile自动集成，无需手动配置。makefile中的相关配置：

```makefile
# CMocka paths
CMOCKA_DIR = Tools/cmocka
CMOCKA_LIB = $(CMOCKA_DIR)/lib/libcmocka.a
CMOCKA_INCLUDE = $(CMOCKA_DIR)/include

# 编译测试时自动包含CMocka
INCLUDES += -I$(CMOCKA_INCLUDE)
```

### 构建和测试

```bash
# 构建项目和测试
make

# 运行单元测试
make run-tests

# 清理构建文件
make clean
```

## 环境要求

项目使用系统默认的编译工具链：

1. **编译器**: 系统安装的 `gcc`
2. **归档工具**: 系统安装的 `ar`
3. **索引工具**: 系统安装的 `ranlib`
4. **系统头文件**: `/usr/include/` 等标准路径

## 优势

1. **版本控制**: CMocka版本固定，确保测试环境一致性
2. **静态链接**: 测试程序不依赖外部CMocka动态库
3. **自包含**: 包含完整的CMocka框架，无需额外安装
4. **跨平台**: 支持不同的Linux发行版

## 故障排除

### 编译失败

如果遇到编译错误：

1. **检查系统编译器**：

   ```bash
   gcc --version
   which gcc
   ```

2. **检查CMocka库文件**：

   ```bash
   ls -la Tools/cmocka/lib/libcmocka.a
   ls -la Tools/cmocka/include/cmocka.h
   ```

3. **检查系统头文件**：

   ```bash
   ls -la /usr/include/stdio.h
   ```

### 测试失败

如果单元测试运行失败：

1. **检查测试程序**：

   ```bash
   ls -la x86_build/test_stepper_motor
   file x86_build/test_stepper_motor
   ```

2. **手动运行测试**：

   ```bash
   ./x86_build/test_stepper_motor
   ```

3. **检查CMocka版本**：

   ```bash
   strings Tools/cmocka/lib/libcmocka.a | grep -i version
   ```

### 清理和重建

如果遇到构建问题，尝试完全清理后重建：

```bash
make clean
make run-tests
```

## 更新CMocka

要更新CMocka到新版本：

1. 备份当前版本：

   ```bash
   mv Tools/cmocka Tools/cmocka.backup
   ```

2. 下载新版本并重新构建（参考项目根目录的构建脚本）

3. 验证新版本：

   ```bash
   make clean
   make run-tests
   ```

## 技术支持

如果遇到问题：

1. 检查系统是否安装了基本的开发工具：

   ```bash
   sudo apt-get install build-essential
   ```

2. 确保有足够的磁盘空间进行编译

3. 检查文件权限，确保Tools目录下的文件可读
