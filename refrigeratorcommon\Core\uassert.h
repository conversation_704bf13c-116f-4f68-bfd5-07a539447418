/*!
 * @file
 * @brief User-defined assert mechanism
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#ifndef UASSERT_H_
#define UASSERT_H_

#include <stdbool.h>
#include <stdio.h>

//cmocka 
#define uassert(condition) \
    do { \
        if (!(condition)) { \
            printf("Assertion failed: %s, file %s, line %d\n", \
                   #condition, __FILE__, __LINE__); \
            while(1); /* Infinite loop to halt execution */ \
        } \
    } while(0)

#endif
